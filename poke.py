# pip install pyusbmux python-imobiledevice
from usbmux import Usbmux
from imobiledevice import Device
import subprocess

# 1. Discover and pair the iPad
mux = Usbmux()
udids = mux.device_list()
print("Connected devices:", udids)

# 2. Query basic info
dev = Device(udid=udids[0])
print("ProductType:", dev.get_value("ProductType"))
print("BuildVersion:", dev.get_value("BuildVersion"))

# 3. Enter Recovery and dump logs
subprocess.run(["idevicepair", "unpair"])
subprocess.run(["idevicepair", "pair"])
subprocess.run(["irecovery", "-c", "dumpenv"])    # dump environment vars in iBoot

# 4. List installed apps
apps = dev.installed_apps()   # requires python-imobiledevice v0.2
print("Installed apps:", apps)

# 5. (Optional) Tunnel lockdownd port to localhost: 
#    Now you can connect to lockdownd RPC over TCP for deeper RPC calls.
mux.forward_port(udids[0], 62078, 62078)
